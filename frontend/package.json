{"name": "Kortix", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"@calcom/embed-react": "^1.5.2", "@codemirror/view": "^6.38.1", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emoji-mart/data": "^1.2.1", "@emoji-mart/react": "^1.1.1", "@hookform/resolvers": "^5.2.1", "@icons-pack/react-simple-icons": "^13.7.0", "@next/third-parties": "^15.3.1", "@number-flow/react": "^0.5.7", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.3", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.8", "@radix-ui/react-toggle": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.8", "@radix-ui/react-use-controllable-state": "^1.2.2", "@react-pdf/renderer": "^4.3.0", "@shikijs/transformers": "^3.12.0", "@silevis/reactgrid": "^4.1.17", "@supabase/ssr": "latest", "@supabase/supabase-js": "latest", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.75.2", "@tanstack/react-query-devtools": "^5.75.2", "@tanstack/react-table": "^8.21.3", "@tiptap/extension-character-count": "^3.3.0", "@tiptap/extension-code-block-lowlight": "^3.3.0", "@tiptap/extension-collaboration": "^3.3.0", "@tiptap/extension-color": "^3.3.0", "@tiptap/extension-details": "^3.3.0", "@tiptap/extension-dropcursor": "^3.3.0", "@tiptap/extension-emoji": "^3.3.0", "@tiptap/extension-font-family": "^3.3.0", "@tiptap/extension-gapcursor": "^3.3.0", "@tiptap/extension-highlight": "^3.3.0", "@tiptap/extension-history": "^3.3.0", "@tiptap/extension-horizontal-rule": "^3.3.0", "@tiptap/extension-image": "^3.3.0", "@tiptap/extension-link": "^3.3.0", "@tiptap/extension-list": "^3.3.0", "@tiptap/extension-mathematics": "^3.3.0", "@tiptap/extension-mention": "^3.3.0", "@tiptap/extension-placeholder": "^3.3.0", "@tiptap/extension-subscript": "^3.3.0", "@tiptap/extension-superscript": "^3.3.0", "@tiptap/extension-table": "^3.3.0", "@tiptap/extension-task-item": "^3.3.0", "@tiptap/extension-task-list": "^3.3.0", "@tiptap/extension-text-align": "^3.3.0", "@tiptap/extension-typography": "^3.3.0", "@tiptap/extension-youtube": "^3.3.0", "@tiptap/pm": "^3.3.0", "@tiptap/react": "^3.3.0", "@tiptap/starter-kit": "^3.3.0", "@types/jszip": "^3.4.0", "@types/papaparse": "^5.3.15", "@types/xlsx": "^0.0.35", "@uiw/codemirror-extensions-langs": "^4.23.10", "@uiw/codemirror-theme-vscode": "^4.23.10", "@uiw/codemirror-theme-xcode": "^4.23.10", "@uiw/react-codemirror": "^4.23.10", "@usebasejump/shared": "^0.0.3", "@vercel/analytics": "^1.5.0", "@vercel/edge-config": "^1.4.0", "@vercel/speed-insights": "^1.2.0", "autoprefixer": "10.4.17", "canvas": "^3.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^0.2.1", "cobe": "^0.6.3", "color": "^5.0.0", "color-bits": "^1.1.0", "colorthief": "^2.6.0", "comment-json": "^4.2.5", "date-fns": "^3.6.0", "diff": "^7.0.0", "extract-colors": "^4.2.0", "file-saver": "^2.0.5", "flags": "^3.2.0", "framer-motion": "^12.6.5", "geist": "^1.2.1", "gsap": "^3.13.0", "hjson": "^3.2.2", "html-to-docx": "^1.8.0", "html2pdf.js": "^0.10.3", "install": "^0.13.0", "jju": "^1.4.0", "json5": "^2.2.3", "jsonrepair": "^3.12.0", "jszip": "^3.10.1", "katex": "^0.16.22", "libphonenumber-js": "^1.12.10", "lodash": "^4.17.21", "lowlight": "^3.3.0", "lucide-react": "^0.479.0", "marked": "^15.0.7", "mermaid": "^11.12.0", "motion": "^12.5.0", "next": "^15.3.1", "next-themes": "^0.4.6", "npm": "^11.5.2", "papaparse": "^5.5.2", "pdfjs-dist": "4.8.69", "postcss": "8.4.33", "posthog-js": "^1.258.6", "posthog-node": "^5.6.0", "radix-ui": "^1.4.3", "react": "^18", "react-color-palette": "^7.3.1", "react-colorful": "^5.6.1", "react-day-picker": "^8.10.1", "react-diff-viewer-continued": "^3.4.0", "react-dom": "^18", "react-hook-form": "^7.62.0", "react-icons": "^5.5.0", "react-joyride": "^3.0.0-7", "react-markdown": "^10.1.0", "react-papaparse": "^4.4.0", "react-pdf": "^9.2.1", "react-phone-number-input": "^3.4.12", "react-scan": "^0.0.44", "recharts": "^3.2.1", "rehype-raw": "^7.0.0", "rehype-sanitize": "^6.0.0", "remark-gfm": "^4.0.1", "server-only": "^0.0.1", "sonner": "^2.0.3", "tailwind-merge": "^3.0.2", "tailwind-scrollbar": "^4.0.2", "tailwind-scrollbar-hide": "^2.0.0", "tailwindcss": "^4", "tailwindcss-animate": "^1.0.7", "turndown": "^7.2.1", "turndown-plugin-gfm": "^1.0.2", "typescript": "^5", "vaul": "^1.1.2", "xlsx": "^0.18.5", "y-indexeddb": "^9.0.12", "yjs": "^13.6.27", "zod": "^3.25.76", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.4", "@types/color": "^4.2.0", "@types/colorthief": "^2.6.0", "@types/diff": "^7.0.2", "@types/jju": "^1.4.5", "@types/lodash": "^4.17.20", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/turndown": "^5.0.5", "encoding": "^0.1.13", "eslint": "^9", "eslint-config-next": "15.2.2", "prettier": "^3.5.3", "shiki": "^3.12.0", "tailwindcss": "^4", "tw-animate-css": "^1.2.4", "typescript": "^5"}}