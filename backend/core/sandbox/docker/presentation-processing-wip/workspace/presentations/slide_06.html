<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Elon Musk: Visionary Entrepreneur - Slide 6</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        /* Base styling and 1920x1080 slide container */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: '<PERSON><PERSON>', <PERSON><PERSON>, sans-serif;
            background: #000000;
            color: #333333;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            overflow: hidden;
        }
        
        .slide-container {
            /* CRITICAL: Standard presentation dimensions */
            width: 1920px;
            height: 1080px;
            max-width: 100vw;
            max-height: 100vh;
            position: relative;
            background: #FFFFFF;
            color: #333333;
            overflow: hidden;
            
            /* Auto-scale to fit viewport while maintaining aspect ratio */
            transform-origin: center center;
            transform: scale(min(100vw / 1920px, 100vh / 1080px));
        }
        
        /* Slide number indicator */
        .slide-number {
            position: absolute;
            bottom: 30px;
            right: 30px;
            font-size: 18px;
            color: #666666;
            font-weight: 500;
            z-index: 1000;
        }
        
        /* Common presentation elements */
        .slide-title {
            font-size: 48px;
            font-weight: bold;
            margin-bottom: 30px;
            color: #005A9C;
        }
        
        .slide-subtitle {
            font-size: 32px;
            margin-bottom: 40px;
            color: #333333;
        }
        
        .slide-content {
            font-size: 24px;
            line-height: 1.6;
            color: #333333;
        }
        
        .accent-bar {
            width: 100px;
            height: 4px;
            background-color: #FF6B00;
            margin: 20px 0;
        }
        
        /* Responsive images */
        img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
        }
        
        /* List styling */
        ul, ol {
            margin: 20px 0;
            padding-left: 30px;
        }
        
        li {
            margin: 10px 0;
            font-size: 20px;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div style='padding: 80px; height: 100%; display: flex; flex-direction: column; justify-content: center;'>
    <h1 style='font-size: 48px; font-weight: bold; color: #005A9C; margin-bottom: 20px; text-align: center;'>Other Ventures</h1>
    <div style='width: 150px; height: 4px; background: #FF6B00; margin: 0 auto 50px;'></div>
    
    <div style='display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 40px; margin-top: 20px;'>
        <div style='padding: 30px; background: #f8f9fa; border-radius: 12px; text-align: center; border-left: 6px solid #FF6B00;'>
            <div style='font-size: 48px; margin-bottom: 20px;'>🧠</div>
            <h3 style='font-size: 24px; color: #005A9C; margin-bottom: 15px;'>Neuralink</h3>
            <p style='font-size: 16px; line-height: 1.5; color: #666;'>Brain-computer interface technology</p>
            <p style='font-size: 14px; color: #999; margin-top: 10px;'>Founded 2016</p>
        </div>
        
        <div style='padding: 30px; background: #f8f9fa; border-radius: 12px; text-align: center; border-left: 6px solid #FF6B00;'>
            <div style='font-size: 48px; margin-bottom: 20px;'>🚇</div>
            <h3 style='font-size: 24px; color: #005A9C; margin-bottom: 15px;'>The Boring Company</h3>
            <p style='font-size: 16px; line-height: 1.5; color: #666;'>Underground tunnel transportation</p>
            <p style='font-size: 14px; color: #999; margin-top: 10px;'>Founded 2016</p>
        </div>
        
        <div style='padding: 30px; background: #f8f9fa; border-radius: 12px; text-align: center; border-left: 6px solid #FF6B00;'>
            <div style='font-size: 48px; margin-bottom: 20px;'>🤖</div>
            <h3 style='font-size: 24px; color: #005A9C; margin-bottom: 15px;'>xAI</h3>
            <p style='font-size: 16px; line-height: 1.5; color: #666;'>Artificial intelligence company</p>
            <p style='font-size: 14px; color: #999; margin-top: 10px;'>Founded 2023</p>
        </div>
    </div>
    
    <div style='display: grid; grid-template-columns: 1fr 1fr; gap: 40px; margin-top: 40px;'>
        <div style='padding: 30px; background: #f8f9fa; border-radius: 12px; text-align: center; border-left: 6px solid #FF6B00;'>
            <div style='font-size: 48px; margin-bottom: 20px;'>🐦</div>
            <h3 style='font-size: 24px; color: #005A9C; margin-bottom: 15px;'>X (formerly Twitter)</h3>
            <p style='font-size: 16px; line-height: 1.5; color: #666;'>Social media platform transformation</p>
            <p style='font-size: 14px; color: #999; margin-top: 10px;'>Acquired 2022 - $44B</p>
        </div>
        
        <div style='padding: 30px; background: #f8f9fa; border-radius: 12px; text-align: center; border-left: 6px solid #FF6B00;'>
            <div style='font-size: 48px; margin-bottom: 20px;'>🏫</div>
            <h3 style='font-size: 24px; color: #005A9C; margin-bottom: 15px;'>Ad Astra School</h3>
            <p style='font-size: 16px; line-height: 1.5; color: #666;'>Innovative education approach</p>
            <p style='font-size: 14px; color: #999; margin-top: 10px;'>Founded 2014</p>
        </div>
    </div>
</div>
        <div class="slide-number">6</div>
    </div>
</body>
</html>