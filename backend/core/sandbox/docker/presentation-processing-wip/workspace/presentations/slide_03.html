<html lang="en"><head><style>body {transition: opacity ease-in 0.2s; } 
body[unresolved] {opacity: 0; display: block; overflow: hidden; position: relative; } 
</style>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>Elon Musk: Visionary Entrepreneur - Slide 3</title>
<link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet"/>
<script src="https://d3js.org/d3.v7.min.js"></script>
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet"/>
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
<style>
        /* Base styling and 1920x1080 slide container */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Roboto', Arial, sans-serif;
            background: #000000;
            color: #333333;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            overflow: hidden;
        }
        
        .slide-container {
            /* CRITICAL: Standard presentation dimensions */
            width: 1920px;
            height: 1080px;
            max-width: 100vw;
            max-height: 100vh;
            position: relative;
            background: #FFFFFF;
            color: #333333;
            overflow: hidden;
            
            /* Auto-scale to fit viewport while maintaining aspect ratio */
            transform-origin: center center;
            transform: scale(min(100vw / 1920px, 100vh / 1080px));
        }
        
        /* Slide number indicator */
        .slide-number {
            position: absolute;
            bottom: 30px;
            right: 30px;
            font-size: 18px;
            color: #666666;
            font-weight: 500;
            z-index: 1000;
        }
        
        /* Common presentation elements */
        .slide-title {
            font-size: 48px;
            font-weight: bold;
            margin-bottom: 30px;
            color: #005A9C;
        }
        
        .slide-subtitle {
            font-size: 32px;
            margin-bottom: 40px;
            color: #333333;
        }
        
        .slide-content {
            font-size: 24px;
            line-height: 1.6;
            color: #333333;
        }
        
        .accent-bar {
            width: 100px;
            height: 4px;
            background-color: #FF6B00;
            margin: 20px 0;
        }
        
        /* Responsive images */
        img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
        }
        
        /* List styling */
        ul, ol {
            margin: 20px 0;
            padding-left: 30px;
        }
        
        li {
            margin: 10px 0;
            font-size: 20px;
            line-height: 1.5;
        }
    </style>

</head>
<body>
<div class="slide-container">
<div style="padding: 80px; height: 100%; display: flex; flex-direction: column; justify-content: center;">
<h1 style="font-size: 48px; font-weight: bold; color: rgb(0, 90, 156); margin-bottom: 20px; text-align: center; position: relative;">First Ventures Test!</h1>
<div style="width: 150px; height: 4px; background: #FF6B00; margin: 0 auto 50px;"></div>
<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 60px; margin-top: 40px;">
<div style="padding: 40px; background: #f8f9fa; border-radius: 12px; border-left: 6px solid #FF6B00;">
<div style="display: flex; align-items: center; margin-bottom: 20px;">
<i class="fas fa-globe" style="color: #005A9C; font-size: 32px; margin-right: 20px;"></i>
<h2 style="font-size: 32px; color: #005A9C; margin: 0;">Zip2</h2>
</div>
<p style="font-size: 20px; line-height: 1.6; margin-bottom: 15px;"><strong>1995-1999</strong></p>
<p style="font-size: 18px; line-height: 1.6; color: #666;">Online city guide software for newspapers</p>
<p style="font-size: 18px; line-height: 1.6; color: #666;"><strong>Exit:</strong><span>Sold to Compaq for $307M</span></p>
</div>
<div style="padding: 40px; background: #f8f9fa; border-radius: 12px; border-left: 6px solid #FF6B00;">
<div style="display: flex; align-items: center; margin-bottom: 20px;">
<i class="fas fa-credit-card" style="color: #005A9C; font-size: 32px; margin-right: 20px;"></i>
<h2 style="font-size: 32px; color: #005A9C; margin: 0;">X.com/PayPal</h2>
</div>
<p style="font-size: 20px; line-height: 1.6; margin-bottom: 15px;"><strong>1999-2002</strong></p>
<p style="font-size: 18px; line-height: 1.6; color: #666;">Online payment system that became PayPal</p>
<p style="font-size: 18px; line-height: 1.6; color: #666;"><strong>Exit:</strong><span>Sold to eBay for $1.5B</span></p>
</div>
</div>
</div>
<div class="slide-number">3</div>
</div>

<readwise-tooltip-container></readwise-tooltip-container></body></html>